require 'will_paginate/array'

class User < ActiveRecord::Base
  include RoleModel

  EMAIL_REGEXP = /\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i

  attr_accessor :current_password

  roles :social, :merchant

  delegate :first_name, :last_name, to: :profile, allow_nil: true

  searchable do
    integer :id, stored: true
    string :network, stored: true
    string :type, stored: true

    text :login
    text :full_name
    time :created_at, stored: true
    time :last_request_at, stored: true
  end

  serialize :braintree_data, Hash
  serialize :mercadopago_data, Hash

  has_one :profile,
    class_name: 'Social::Profile::User',
    dependent: :destroy,
    foreign_key: :user_id

  has_many :addresses,
           class_name: 'Mkp::Address',
           as:         :addressable,
           dependent:  :destroy

  has_many :carts,
           class_name: 'Mkp::Cart',
           as:        :customer,
           dependent: :destroy

  has_many :mkp_answers,
           class_name: 'Mkp::Answer',
           dependent: :destroy

  has_many :mkp_questions,
           class_name: 'Mkp::Question',
           dependent: :destroy

  has_many :notifications,
           dependent: :destroy

  has_many :orders,
           class_name: 'Mkp::Order',
           as:         :customer

  has_many :shop_admins,
           class_name: 'Mkp::ShopAdmin',
           foreign_key: 'admin_id',
           dependent: :destroy

  has_many :shops,
           class_name: 'Mkp::Shop',
           through: :shop_admins

  has_many :comments, class_name: 'Social::Comment', dependent: :destroy
  has_one :office, class_name: 'Mkp::Bna::Office', dependent: :destroy


  after_create :check_avenida_user

  acts_as_authentic do |c|
    c.transition_from_crypto_providers = [Authlogic::CryptoProviders::Sha512]
    c.crypto_provider = Authlogic::CryptoProviders::SCrypt
  end

  validates :roles_mask, presence: true
  validates :network, inclusion: { in: [Network.all_active, nil].flatten }
  validates :password, confirmation: true
  validates :login, login_available: true
  validates :login, format: { with: /\A^[a-z][a-z0-9\-\.\_]+$\z/i }
  validates :login, length: { within: 3..25 }

  validates :email, presence: true
  validates :email, format: EMAIL_REGEXP


  # validates_associated :profile

  # accepts_nested_attributes_for :profile

  scope :random, ->(number){ order('RAND()').limit(number) }
  scope :by_network, ->(network){ network.present? ? where(network: network) : all }
  scope :with_aggregated_profiles, -> { joins(:aggregated_profiles).where(['aggregated_profiles.id IS NOT ?', nil]) }

  delegate :avatar, :cover, to: :profile, prefix: false, allow_nil: true

  def self.find_by_anything(login)
    find_by_login(login) || find_by_email(login)
  end

  def managed_users
    Brand.managed_by(self) + [self.id]
  end

  def check_avenida_user
    if email.match? '@avenida.com'
      Mkp::Shop.ids.each{ |shop| Mkp::ShopAdmin.create(shop_id: shop, admin_id: self.id, owner: false) }
    end
  end

  def owned_by?(user)
    self == user
  end

  def owns_a_shop?
    !owned_shop.nil?
  end

  def owned_shop
    shops.detect { |s| s.owned_by?(self) }
  end

  def owns_a_brand?
    is_a?(Brand)
  end

  def owned_brand
    is_a?(Brand) ? self : nil
  end

  def cart(network)
    Mkp::Cart.cart(carts, network)
  end

  def stores
    shops.map(&:stores).flatten.uniq
  end

  def get_or_create_regular_cart(network)
    carts.where('type is null').where(network: network).first || carts.create!(network: network)
  end

  def stores
    shops.map(&:stores).flatten.uniq
  end

  def guest?
    roles_mask == nil
  end

  def language
    profile.language
  end

  # Returns true if the following action
  # could was successful, false otherwise
  def follow(*items)
    items.each do |i|
      if i.is_a?(User)
        follows << follows.new(followed: i)
      elsif i.is_a?(Sport)
        profile.add_followed_sport(i.id)
      else
        raise ArgumentError, 'Only users and sports can be followed'
      end
    end
  end

  # Returns true if the unfollowing action
  # could was successful, false otherwise
  def unfollow(*items)
    begin
      items.each do |i|
        if i.is_a?(User)
          _follow = follows.where( followed_id: i.id,
                                   followed_type: ['User', i.class.to_s] ).first
          _follow.delete if _follow.present?
          return true
        elsif i.is_a?(Sport)
          profile.remove_followed_sport(i.id)
          return true
        end
      end
    rescue
      raise StandardError.new('could not be unfollowed')
    end
  end

  def follows?(entity)
    if entity.is_a?(User)
      followed_users.where(id: entity.id).any?
    elsif entity.is_a?(Sport)
      followed_sports.where(id: entity.id).any?
    else
      false
    end
  end

  def follows_by_id?(entity_id, entity_type)
    if entity_type.new.is_a?(User)
      followed_users.where(id: entity_id).any?
    elsif entity_type.new.is_a?(Sport)
      followed_sports.where(id: entity_id).any?
    else
      false
    end
  end

  def followed_sports
    profile.followed_sports
  end

  def full_name
    login || ""
  end

  def update_with_password(params, *options)
    current_password = params.delete(:current_password)

    valid_password = valid_password?(current_password)
    if valid_password
      update_attributes(params, *options)
    else
      assign_attributes(params, *options)
      valid?
      errors.add(:current_password, current_password.blank? ? :blank : :invalid)
    end

    valid_password
  end

  def locale
    Network.for(network).locale
  end

  # Function that tries to find a related manufacturer searching it by
  # alike names
  @@mfrs_slugs_cache = nil
  def search_for_related_manufacturer
    if @@mfrs_slugs_cache.blank?
      @@mfrs_slugs_cache = Mkp::Manufacturer.all.map do |m|
        { id: m.id, simplified_name: simplify_string(m.name) }
      end
    end

    simplified_name = simplify_string(full_name)
    manufacturer = @@mfrs_slugs_cache.find do |m|
      m[:simplified_name] == simplified_name
    end

    manufacturer.present? ? Mkp::Manufacturer.find(manufacturer[:id]) : nil
  end

  def eshop_user?
    login == 'eshop' || email.include?('@avenida.com')
  end

  def is_bna_administrator?
    email.downcase.include?('@bna.com.ar')
  end

  def movilidad_superuser?
    email.downcase.include?('movilidad.superuser')
  end

  private

  def simplify_string(name)
    name.gsub(/[^0-9a-z]/i, '').downcase
  end
end
