.row
  .card.card-default.panel.panel-default
    .panel-heading
      .panel-title
        #{t('pioneer.shop.admin-title')}
    .panel-body
      .col-md-6
        %h4.title #{t('pioneer.shop.admin-owner')}
        - owner = shop.owner
        - if owner.present?
          = render partial: 'pioneer/users/user_ficha', locals: { user: owner }
        - else
          %p #{shop.title} #{t('pioneer.shop.admin-no-owner')}
      .col-md-6
        %h4.title #{t('pioneer.shop.admin-admin')}
        - managers = shop.admins.select { |admin| !shop.owned_by?(admin) }
        - if managers.present?
          = render partial: 'pioneer/users/user_ficha', collection: managers, as: :user
        - else
          %p #{shop.title} #{t('pioneer.shop.admin-no-admin')}

        - if owner.present? && owner.is_a?(Brand)
          %br
          - if can?(:crud, 'Brands')
            %a.btn.btn-primary.mt-2{ href: edit_brand_path(owner) } #{t('pioneer.shop.admin-change')}
        - else
          %p{ style: 'color:red; font-size: 12px' } #{t('pioneer.shop.admin-no-owner-admin')}
