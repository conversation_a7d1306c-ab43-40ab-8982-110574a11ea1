.p-4
  - if @store.persisted?
    .form-group
      = f.label :name, t('pioneer.stores.name')
      = f.text_field :name, required: true, disabled: true, class: 'form-control', label: false
  .row.my-4
    .col-sm-12.col-md-6
      .form-group
        = f.label :title, t('pioneer.stores.header')
        = f.text_field :title, required: true, class: 'form-control', label: false
    .col-sm-12.col-md-6
      .form-group
        = f.label :logo, t('pioneer.stores.logo-url')
        = f.text_field :logo, required: true, class: 'form-control', label: false
  .row.my-4
    .col-sm-12.col-md-6
      .form-group
        = f.label :logo, t('pioneer.stores.store-url')
        = f.text_field :hostname, required: true, class: 'form-control', label: false
    .col-sm-12.col-md-6
      .form-group
        = f.label :popup_image, t('pioneer.stores.newsletter-image')
        = f.text_field :popup_image, class: 'form-control', label: false
  .row.my-4
    .col-sm-12.col-md-6
      .form-group
        = f.label :email, t('pioneer.stores.email')
        = f.text_field :email, class: 'form-control', label: false
    .col-sm-12.col-md-6
      .form-group
        = f.label :telephone, t('pioneer.stores.telephone')
        = f.text_field :telephone, class: 'form-control', label: false
  .row.my-4
    .col-sm-12.col-md-6
      .form-group
        = f.label :percentage_fee, t('pioneer.stores.cash-discount')
        = f.number_field :percentage_fee, :value => 0, max: 50, min: 0, class: 'form-control', label: false
    .col-sm-12.col-md-6
      .form-group
        = f.label :image_dues, t('pioneer.stores.image-dues')
        = f.file_field :image_dues, class: 'form-control', label: false
  .row.my-4
    .col-sm-12.col-md-6
      .form-group
        = f.label :preferred_sorting, t('pioneer.stores.preferred_sorting')
        = select_tag :preferred_sorting, options_for_select([['Aleatoriamente', 'random'], ['Más relevantes (descendentemente)', 'score:desc'], ['Más relevantes (ascendentemente)', 'score:asc']], @store.preferred_sorting.map(&:to_s).join(':')), class: 'form-control', name: 'mkp_store[preferred_sorting]', :prompt => "Seleccione"
    .col-sm-12.col-md-6
      .form-group
        = f.hidden_field :limit_amount, id: 'hidden_limit_amount'
        = f.number_field :limit_amount_input, class: 'form-control', value: f.object.limit_amount || 0, id: 'limit_amount_input', step: 'any'
        = text_field_tag :formatted_limit_amount, nil, class: 'form-control', disabled: true, id: 'formatted_limit_amount'

        javascript:
          document.addEventListener('DOMContentLoaded', function() {
            const numberField = document.getElementById('limit_amount_input');
            const hiddenField = document.getElementById('hidden_limit_amount');
            const formattedField = document.getElementById('formatted_limit_amount');

            function formatNumberWithDelimiter(number) {
              return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }

            function updateFormattedField() {
              const value = parseFloat(numberField.value) || 0;
              hiddenField.value = value;
              formattedField.value = formatNumberWithDelimiter(value);
            }

            numberField.addEventListener('input', updateFormattedField);

            updateFormattedField();
          });
  .py-4.mt-4.border-top
    h4 #{t('pioneer.stores.points-equivalence-title')}
    .row.my-4
      .col-sm-12.col-md-6
        .form-group
          = f.number_field :equivalent_points, step: '0.1', max: 5, min: 0, class: 'form-control', label: t('pioneer.stores.points-equivalence')
  .py-4.mt-4.border-top
    h4 #{t('pioneer.stores.social-media')}
    .row.my-4
      .col-sm-12.col-md-6
        .form-group
          = f.text_field :instagram, class: 'form-control', label: "Instagram"
      .col-sm-12.col-md-6
        .form-group
          = f.text_field :twitter, class: 'form-control', label: "Twitter"
    .row.my-4
      .col-sm-12.col-md-6
        .form-group
          = f.text_field :facebook, class: 'form-control', label: "Facebook"
  .py-4.mt-4.border-top
    h4=t('pioneer.stores.configuration')
    .row.my-4
      .col-sm-12.col-md-3
        .checkbox
          = f.check_box :active, label: t('pioneer.stores.is-active')
        .checkbox
          = f.check_box :invoiceable, label: t('pioneer.stores.invoces')
      .col-sm-12.col-md-3
        .checkbox
          = f.check_box :inherit_free_shipping, label: t('pioneer.stores.is-free-shipping')
        .checkbox
          = f.check_box :abandoned_cart_email, label: t('pioneer.stores.abandoned-cart')
      .col-sm-12.col-md-3
        .checkbox
          = f.check_box :product_approval, disabled: true, label: t('pioneer.stores.product-approval')
        .checkbox
          = f.check_box :allow_orders_cancellation, label: t('pioneer.stores.allow-orders-cancellation')
  .py-4.mt-4.border-top
    = f.fields_for :free_shipping do |ff|
      h4=t('pioneer.stores.free-shipping')
      .row.my-4
        .col-sm-12.col-md-6
          .form-group
            = ff.date_field :from, label: "#{t('pioneer.stores.from')}", class: 'form-control'
        .col-sm-12.col-md-6
          .form-group
            = ff.date_field :to, label: "#{t('pioneer.stores.to')}", class: 'form-control'
      .row.my-4
        .col-sm-12.col-md-6
          .form-group
            = ff.label "#{t('pioneer.stores.amount')}"
            = ff.number_field :amount, step: '0.1', label: false, class: 'form-control'
        .col-sm-12.col-md-3
          .checkbox
            = ff.check_box :active, label: "#{t('pioneer.stores.active')}"
  .py-4.mt-4.border-top
    h4 #{t('pioneer.stores.insurances')}
    .row.my-4
      .col-sm-12.col-md-6
        .form-group
          = f.select :insurance_source, options_for_select(Mkp::Store.insurance_sources.map {|key, _value| [t(key, scope: 'pioneer.stores.insurance_types'), key]}, selected: @store.insurance_source), { include_blank: false, label: "#{t('pioneer.stores.w-insurance')}" } , class: "form-control required"
  .py-4.mt-4.border-top
    = f.fields_for :cancelation_service_configuration do |ff|
      h4 #{t('pioneer.stores.cancelation-service-configuration')}
      .row.my-4
        .col-sm-12.col-md-6
          .form-group
            .checkbox
              = ff.check_box :generate_refund_shipments, label: t('pioneer.stores.generate-refund-shipments')
            .checkbox
              = ff.check_box :generate_coupons, label: t('pioneer.stores.generate-coupons')
            .checkbox
              = ff.check_box :generate_credit_notes, label: t('pioneer.stores.generate-credit-notes')
            .checkbox
              = ff.check_box :cancel_payments, label: t('pioneer.stores.cancel-payments')
