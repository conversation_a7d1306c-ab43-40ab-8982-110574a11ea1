namespace :cleanup do
  desc "Elimina archivos de exports (sales/products) de /public con más de 7 días de antigüedad"
  task :old_sales_products_exports => :environment do
    public_path = Rails.root.join('public')
    cutoff_time = 7.days.ago
    deleted_count = 0

    Dir.glob(File.join(public_path, '**', '*')).each do |file_path|
      next unless File.file?(file_path)

      filename = File.basename(file_path)
      next unless filename.start_with?('sales', 'products', 'shops')

      if File.mtime(file_path) < cutoff_time
        begin
          File.delete(file_path)
          deleted_count += 1
          Rails.logger.info "Eliminado archivo: #{file_path}"
        rescue => e
          Rails.logger.error "Error eliminando #{file_path}: #{e.message}"
        end
      end
    end

    Rails.logger.info "Eliminados #{deleted_count} archivos de #{public_path}"
  end
end
