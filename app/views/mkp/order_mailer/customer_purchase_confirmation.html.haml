- I18n.locale = @locale
%div{ :style => "color:#525252; font-family: verdana, geneva, sans-serif; font-size:11px;"}
  %div.body
    %h2
      =t('.hi',name: @customer.first_name, last:@customer.last_name)
    %p
      = "#{@order.shipment_is_pickup? ? t('.confirmation_pickit') : t('.confirmation')}"
    - if @current_store.name == 'bancomacro'
      %table.order-item{ :style => "width: 100%;" }
        %tbody
          - @order.items.each do |item|
            = render partial: 'mailer/partials/order_item_shipment', locals: {item: item}
    - else
      - @order.suborders.each do |suborder|
        - suborder.items.each do |item|
          = render partial: 'mailer/partials/order_item', locals: {item: item}
        - if suborder.shipment.virtual?
          = ' '
        - else
          - if suborder.shipment.pickable?
            = render partial: 'mailer/partials/shipped_is_pickup', locals: {shipment: suborder.shipment}
          - elsif !(@order.shipments.map(&:shipment_kind).all?("delivery"))
            = render partial: 'mailer/partials/shipped', locals: {shipment: suborder.shipment}
    - shipment = @order.shipments.last
    - if @order.shipments.map(&:shipment_kind).all?("delivery")
      = render partial: 'mailer/partials/shipped', locals: {shipment: shipment}
    %br
    .highlight{ :style => "padding: 15px; background-color: #f6f6f6; border-radius: 6px;" }
      .container-highlight
        %h4
          = t('.summary')
        - if @current_store.name == 'bancomacro'
          %p{ :style => "margin-top: 20px" }
          %strong
            - payments = @order.payments.where.not(gateway: ['VisaPuntos', 'SystemPoint'])
            - if payments.any?
              = number_to_currency(@order.total_without_points, unit: current_currency_format)
              - installments = payments.first.government_installments
              - if installments.present?
                - installments_text = installments == 1 ? 'cuota' : 'cuotas'
                = "(#{installments} #{installments_text} de #{number_to_currency((@order.total_without_points / installments), unit: current_currency_format)})"
              - if @order.points > 0
                = " y "
                = number_with_delimiter(@order.total_points, :delimiter => '.')
                = " Puntos Macro Premia"
            - else
              - if @order.points > 0
                = number_with_delimiter(@order.total_points, :delimiter => '.')
                = " Puntos Macro Premia"
        - else
          - if @order.has_coupon? && @order.bonified_amount == 0
            %p
              = t('.coupon_bonification') + ": "
              %strong
                = number_to_currency @order.coupons_discount, unit: current_currency_format
          - if @order.avg_of_discount_applied > 0
            %p
              = t('.discount') + ": "
              %strong
                = t('.applied_discount',avg:@order.avg_of_discount_applied)
          %p
            = t('.shipping_cost') + ": "
            %strong.currency
              = number_to_currency(@order.shipments_cost, unit: current_currency_format)
          - if @order.bonified_amount > 0
            %p
              = t('.subtotal') + ": "
              %strong
                = number_to_currency(@order.subtotal_with_shipment, unit: current_currency_format)
            %p
              = t('.bonified_amount') + ": "
              %strong
                = number_to_currency(@order.bonified_amount_total, unit: current_currency_format)
          %p
            = t('.financing_cost')
            %strong
              = "$#{@order.payment.financial_cost_value}"
          %p
            - if @order.points <= 0
              = t('.total') + ": "
              %strong
                = number_to_currency((@order.total_without_points), unit: current_currency_format)

            - if @order.points > 0
              = t('.total') + ": "
              %strong
                = number_with_precision(@order.total_points, precision: 0)
                = " puntos + "
                = number_to_currency(@order.total_without_points, unit: current_currency_format)
          - if @current_store.name == 'bna'

            - if @order.shipments.map(&:shipment_kind).any?("delivery")
              %p{style:"text-align: justify;"}
                =t('.legal_bna')

            %p{style:"text-align: justify;"}
              =t('.legal_bna_2')
              %a{ href: "https://www.tiendabna.com.ar/ayuda", target: "_blank"}
                ="link."
            %p{style:"text-align: justify;"}
              =t('.legal_bna_3')

            - if @order.shipments.map(&:shipment_kind).any?("delivery")
              %p{style:"text-align: justify;"}
                =t('.legal_bna_4')
            - else
              %p{style:"text-align: justify;"}
                =t('.legal_bna_5')
            %p{style:"text-align: justify;"}
              =t('.legal_bna_6')
            %p{style:"text-align: justify;"}
              =t('.legal_bna_7')
            %p{style:"text-align: justify;"}
              =t('.legal_bna_8')
            %p{style:"text-align: justify;"}
              =t('.legal_bna_9')


    - if @current_store.name == 'paseolibertad'
      %p{:style => "text-align: justify;"}
        =t('.legal6')
  %br
  = render partial: 'v5/partials/mailer/contact_us'
