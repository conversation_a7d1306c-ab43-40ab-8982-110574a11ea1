module Social
  module Attachment
    class AvatarPicture < Picture
      has_attached_file :photo,
                        default_url: Profile::DEFAULT_AVATAR_URL,
                        styles: ->(a){ a.instance.class.styles },
                        processors: [:thumbnail, :paperclip_optimizer]

      def self.styles
        {
          t: '100x100#', # cropped
          m: '500x300>' # max sizes, with aspect ratio
        }
      end

      FALLBACK_COLORS = %w( #34495e #9b59b6 #3498db #2ecc71 #1abc9c #3498db
                            #f1c40f #e67e22 #e74c3c #95a5a6 #16a085 #27ae60
                            #2980b9 #8e44ad #2c3e50 #f39c12 #d35400 #c0392b
                            #7f8c8d )

      def self.fallback_color_for(customer)
        FALLBACK_COLORS[customer.id % FALLBACK_COLORS.length]
      end

      def self.fallback_text_for(customer)
        customer.full_name.split(' ').slice(0..1).map{ |s| s[/[a-z]/i] }.join.upcase
      end

      def url(style = :t)
        if Rails.env.development?
          return 'https://via.placeholder.com/100x100/cccccc/666666?text=Avatar'
        end

        if processing
          style = :m unless style == :original
          unpublished_photo(style, :url)
        else
          photo.url(style)
        end
      end
    end
  end
end
