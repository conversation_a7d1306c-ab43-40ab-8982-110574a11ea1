class ShopProductsRefreshWorker
  include Sidekiq::Worker

  sidekiq_options queue: :solr, retry: 3, backtrace: true

  def perform(shop_id)
    shop = Mkp::Shop.find(shop_id)

    Rails.logger.info "🚀 [SHOPADMIN-APAGADO] Iniciando refresh asíncrono para shop #{shop.id} (#{shop.title}) con #{shop.products.count} productos"

    start_time = Time.current

    begin
      shop.products_refresh_optimized

      duration = (Time.current - start_time).round(2)
      Rails.logger.info "✅ [SHOPADMIN-APAGADO] Refresh asíncrono completado para shop #{shop.id} en #{duration}s"

    rescue => e
      Rails.logger.error "❌ [SHOPADMIN-APAGADO] Error en refresh asíncrono para shop #{shop.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      raise e
    end
  rescue ActiveRecord::RecordNotFound => e
    Rails.logger.error("[SHOPADMIN-APAGADO] ShopProductsRefreshWorker: Shop #{shop_id} not found - #{e.message}")
  end
end
