development: &app_config
  qr:
    qr_id_app: "c699e58c-b0a5-11ef-96d7-005056010f98"
    qr_country: "ARG"
    qr_idmaqc_service: "ONBOARDING_DIGITALPLATFORM"
    qr_profile_services: "ONBOARDING_DIGITALPLATFORM"
    qr_services: "ONBOARDING_DIGITALPLATFORM"
    qr_client_id: "CiC5VhBh9Sf6xipdqIs3F7XUlEwZdSnz"
    qr_client_secret: "8jb5hg2YLm37YyAKUAovsnKcTHj2jcsj"
    qr_provision_key: "iwT5kwHNVGTflyaXr0qYvq3MO4y2836R"
    qr_apikey: "AR_NSERVINT_WB"
    qr_base_url: 'https://admin.4i4id.com:4443'
    qr_base_url_qrcomponent: 'https://admin.4i4id.com:3443'
    jwt: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI0MzIzQWFidHpJTzZFdDFqajJkV0N0SEpVMkxiRURrSiJ9.3Z2bjlr_GeKrvuRYde7Z3sLFxSudlejXQpdIvreVjjU"
    iss: "4323AabtzIO6Et1jj2dWCtHJU2LbEDkJ"
    signature: "Rx0QXNAsRustVnQZ7GMKynDAkdVw8CjK"
    qr_seckey: "614639b0-3f4a-40e8-bb9c-f0e34222b927"
    qr_session_id_auth: "c0242f04-6c71-438e-b32a-bd1c2a87d9ee"
    response_inf: "https://api-bna.com.ar/biometry_response"
    qr_front_decision_base_url: "https://tiendabna-stg.avenida.com.ar/checkout"
  modo:
    bna:
      default_external_program_id: '1234'

  avenida-payments:
    url: 'https://api-payments-dev.avenida.com/auth/v1'
    mercadopago:
      url: https://api.mercadopago.com/v1/card_tokens
      public-key:
  rocketchat:
    platform-token: '635302f82ecd34843e81d34e/kotFMxS79f84JfTKZ54fSPewWP2nYbETqLr8TXdKuS3wPMDP'
    payments-token: '626165960a652d000831c8a1/AShcxh2jYdjQWEyTu2cerLR3GYRdoDDyNrEmCQdhREYhxtKv'
    blister-token: '63752040daa50ed2512b1c0d/aDeHGxTZmswrwYTE38SDXfJR6A2EAvYKHvstDtzK6TeHnbML'
  nacion_tokens:
    url: https://api-nacion-token.testing.avenida.com/
    api_key: cambiar_1234
  renaper:
    url: https://identity-validation-api.sube-staging.nacionservicios.com.ar/
    url_auth: https://auth.sube-staging.nacionservicios.com.ar/auth/realms/sube-pagos/protocol/openid-connect/token
    grant_type: password
    client_id: bna-conecta
    username: bna-conecta
    password: BNAConecta1234
  affiliates:
    pampa:
      us:
        param_name: 'affiliateid'
        api_token: 'US_PampaAPI_Token'
        account_id: 'US_PampaAccount_ID'
        campaign_id: 'US_PampaCampaign_ID'
        currency: 'USD'
      ar:
        param_name: 'affiliateid'
        api_token: 'AR_PampaAPI_Token'
        account_id: 'AR_PampaAccount_ID'
        campaign_id: 'AR_PampaCampaign_ID'
        currency: 'ARS'

  atene:
    url: "http://athenee-api-dev.theamalgama.com"
    keys:
      username: '<EMAIL>'
      password: 'example123'
      grant_type: 'password'

  boca:
    url: 'http://sgs-test.bocajuniors.com.ar:8080/sgs-rfs/services'
    key: '$2a$10$LaB9G0Qm7/f5XfkpWk.a0.2BoSQeUDM76kyABAJ6PKK6x4YlWtJ0q'


  aws:
    access_key_id:
    secret_access_key:
    region:
    tdd:
      bucket:
    emblue:
      access_key_id: 'your_access_key'
      secret_access_key: 'your_secret_key'
      region: 'your_region'
      bucket: 'you_bucket_name'

  angular:
    api_key: 'your_access_key'

  irsa:
    chequeregalo:
      url: https://example.irsacorp.com.ar/connect/token
      grant_type: client_credentials
      client_id: gp
      client_secret: example

  jubilo:
   production:
     url:
   staging:
     url: 'https://webapitest.jubilo.com.ar:44301'

  brukman:
    url: "https://virtualseller-7930.cloudforce.com"
    auth_url: "https://virtualseller.cloudforce.com/services/oauth2/token"
    keys:
      grant_type: 'password'
      client_id: ''
      client_secret: '3367300000407281615'
      username: '<EMAIL>'
      password: 'Pepo'

  braintree:
    merchant_id: 'bp9h294hdfzqpmnd'
    public_key: '5j6fjvtftwy2k9wd'
    private_key: '509d7e8bee5f441254eeeaf487ab68f9'
    env: 'sandbox'

  colppy:
    client:
      username: '<EMAIL>'
      password: '4b6307b1d3127146a2491baf569ee591'
    company_id: '10516'
    user:
      username: '<EMAIL>'
      password: '0738ea47f561f781da212cf482a1fbb4'

    sportclub:
      client:
        username: ''
        password: ''
      company_id: ''
      user:
        username: ''
        password: ''

  sportclub:
    endpoint: 'https://api.sportclub.com.ar'
    token: 'token'

  easypost_api_key: 'PSmNT4LTwGWMDaaDktCLgQ' # Test API Key

  enable_sidekiq_web: true

  external_communications:
    key: '6cd5bb8a4ba03482019a2e7a7cd98b2a'

  facebook_api:    '***************'
  facebook_secret: '********************************'
  facebook_pixel:
    ar:
      tracking_id: ''
    us:
      tracking_id: ''

  google:
    analytics:
      tracking_id: 'UA-7444591-2'
      domain: 'v2-staging.goodpeople.com'
    analytics_reporter_account:
      key_path: 'config/keys/dev-test-analytics-reporter.p12'
      issuer: '<EMAIL>'
    api: ''
    conversion_id:    '1234'
    tag_manager:
      container_id: 'GTM-KDZG78'

  hostname: 'http://0.0.0.0:3000'

  include_analytics: true
  include_olark_chat: false

  issuu_api:       '6lo8iz8e34hoavt94ztk2iwvhpo5ht9h'
  issuu_secret:    'xrcbpkerwqnssavig77nt14nt8m7g22q'

  lion_key: "4E9078F5-DF24-4BB5-82B2-0B48DA6CE3BC"

  mailchimp:
    api_key: ''
    lists:
      avenida:
        subscribers_id: ''
        popup_id: ''
        buyers_id:
      tiendalanueva:
        subscribers_id: ''
        popup_id: ''
        buyers_id:

  mercadolibre:
    app_key:        '***************'
    app_secret:     '********************************'
    AR:
      uid: '*********'
      account_name: '<EMAIL>'
    callback_url:   'http://localhost:3000'
    site_country:   'MLA'

  mercadopago:
    old:
      client_id:     '9444'
      client_secret: '********************************'
    new:
      public_key: 'TEST-05343ed9-fc8c-4994-8723-f08059425e2d'
      access_token: 'TEST-12128-050413-ea866dd236eee1b6ca89e569b9eb88d6__LA_LB__-********'

  decidir:
    public_key: "e9cdb99fff374b5f91da4480c8dca741"
    private_key: "92b71cf711ca41f78362a7134f87ff65"
    endpoint: "https://developers.decidir.com/api/v2"
    # desarrollo: endpoint: "https://developers.decidir.com/api/v1"
    # productivo: endpoint: "https://live.decidir.com/api/v1"
    # avenida_site_id: "********"
    avenida_jr_site_id: "********"
    tdd:
      public_key: ""
      private_key: ""
    bancociudad:
      public_key:
      private_key:

  todopago:
    app_key:  'PRISMA A793D307441615AF6AAAD7497A75DE59'
    merchant: '2159'
    security: 'A793D307441615AF6AAAD7497A75DE59'
    endpoint: 'https://developers.todopago.com.ar'
    authorize_url: 'http://localhost:3000'
    hybrid_js: 'https://developers.todopago.com.ar/resources/TPHybridForm-v0.1.js'

  tarjeta_digital:
    domain: 'https://qa.ccpaysrv.quasarbox.com'
    user: 'avenida'
    password: 'avenidaavenida'
    retailer: 100840

  firstdata:
    domain: 'https://test.ipg-online.com/ipgapi/services/order.wsdl'
    # produccion domain: 'https://www5.ipg-online.com/ipgapi/services/order.wsdl'
    # desarrollo domain: 'https://test.ipg-online.com/ipgapi/services/order.wsdl'

  oca_epak:
    account_number: ''
    cuit: ''
    password: ''
    user: ''

  on_staging: false

  string:
    security: "security"

  system_point:
    url: http://localhost:9292
    tddboca:
      private_key: secret
    bancomacro:
      private_key: secret
  paperclip:
    s3_host_alias: 'avenida-dev.s3.amazonaws.com'
    bucket: 'avenida-dev'

  pickit:
    endpoint: 'https://core.pickitlabs.com/app.php/CallMethod'
    api_key: '43EVPYXV7W'
    token_id: 'P4NB49QYE6'

  redis:
    host: 'localhost'
    port: 6379
    db:   0
    url:  'redis://localhost:6379/0'

  secret_token: '9cb236a95769c4f3c1b5723e5a9aed9e11b1af64832c3971f80a544db5dc19c0b13bb4b795ff8aecd6f365f1c1ccec9247ae0c03429eb16ba0d97d79f04e6676'

  sendgrid:
    api_key: '*********************************************************************'
    subscribers_list_id: '800311'

  shipnow:
    api_endpoint: 'https://api-staging.shipnow.com.ar'
    access_token: 'xRKUNC1FqoMCNiOgSpjafZgwKcKfVDB4plie6_S6RfcSsPdGyw'
    admin_endpoint: 'sellers-staging.shipnow.com.ar'
    store_id: 118
    meli_store_id: 117

  smart_carrier:
    api_key: '5Dgvqqmxtp4B9KzUu1dgfVkGq7RfAPBx'
    url: 'http://test.smartcarrier.redbee.io/api'

  shopify:
    api_key: 'c01d66cff1008b8074b1e5a7d922fcfd'
    secret: '********************************'

  ssl_enabled: false

  twitter_api:     'tXbpF4rSTkGcJ1XeDqhtgw'
  twitter_secret:  'tuESMI1d3rdpEPuk3DZ9w2OD8HzahOmWiAjkK163mI'

  veinteractive:
    enabled: false
    tag: '//configusa.veinteractive.com/tags/68B6E1E9/5044/473E/8B6C/1786B4748263/tag.js'
    pixel: '//cdsusa.veinteractive.com/DataReceiverService.asmx/Pixel?journeycode=68B6E1E9-5044-473E-8B6C-1786B4748263'

  sidekiq:
    user: "SIDEKIQ"
    password: "PASSWORD"

  visa_puntos:
    default_api_key: '30e10594-ff57-4fd9-b2d3-169a7d53bb6a'
    endpoint: 'http://test-gateway.avenida.com.ar:83/v1/'
    cod_premio: 'V5010001'

  macro_login:
    endpoint: 'http://test-gateway.avenida.com.ar:82/v1'
    api_key: '9aeac789-96ed-4900-a7cc-313fae7cd1e2'

  technisys:
    url: 'https://***************'

  aerolineas_argentinas:
    url: 'https://pswtest.aerolineas.com.ar'

  krabpack:
    url: 'https://api.test.krabpack.com'
    api_key: uOpLhuSRWslMCOJRdGBUZOXNuBfPRRiP
    macro_api_key: uOpLhuSRWslMCOJRdGBUZOXNuBfPRRiP

  pyp:
    recarga_url: 'https://staging.logistica.puntosypremios.com.ar'
    catalogo_url: 'https://pyp-microsites-staging.herokuapp.com'
    recarga_catalogo_id: 'avenida_recargas'
    recarga_api_key: '366dd428338752ee'
    product_catalog_id: 'macro_recargas_avenida'
    product_api_key: 'qY-cpD3_2KFFH61tC0wjXA'

  sube:
    url: 'http://localhost:9000/api/v1/'

  emblue:
    auth_token: 'Basic NjRjMjM5MWVlNTY0NGNmMmJhZGFlN2QwODM4NGRmMWU='
    api_endpoint: 'http://track.embluemail.com/contacts'
    time_lapse: 30
    mailer:
      auth_token: 'Basic NjRjMjM5MWVlNTY0NGNmMmJhZGFlN2QwODM4NGRmMWU='
      api_endpoint: 'http://track.embluemail.com/contacts'

  rollbar:
    access_token: '316a3077d2624ec5aa9148d15983a319'

  ochenta:
    api_endpoint: 'https://apiavenidatest.azurewebsites.net/api/v1'
    shop_id: 2126
    category_id: 3543
    manufacturer_id: 4133
    username: 'apiAvenida'
    password: 'VFkcwR9bxd'
    client_id: 'dc9pNMcXNt@gi6veF6bR_r7op_vmTq2d:S,C_xHBkQfywdozf3h8ycEq6bcOicrDN+edzscdLG1Z-4iW-wRuSg3D:1kUf3C+yfjH'
    external_program_id: 1234

  blister:
    api_endpoint: 'https://api-marketplace.staging.andromedalatam.com'
    username: '8xmIvrE0PX'
    password: 'yF0gkJt9sL'

  bna_loyalty:
    check_points: false
    base_url: "https://pb-api-stg.nacionservicios.com.ar"
    sso_url: "https://nssa-secure-sso-uat.nacionservicios.com.ar/auth"
    sso_client_id: "pb-api"
    sso_client_secret: "4fc25ce7-ecef-47e5-8726-310e94939bc0"
    sso_scope: "openid"
    sso_token_endpoint: "/realms/programa-beneficios/protocol/openid-connect/token"
    sso_username: "tienda-bna-pb-user"
    sso_password: "L06AdhlMlU"
    points_money: 0.5
  disable_sql_logs: false

production:
  <<: *app_config

profile:
  <<: *app_config

test:
  <<: *app_config
  braintree:
    merchant_id: 'bp9h294hdfzqpmnd'
    public_key: '5j6fjvtftwy2k9wd'
    private_key: '509d7e8bee5f441254eeeaf487ab68f9'
    env: 'sandbox'

  capybara:
    javascript_driver: :selenium # options are :selenium | :poltergeist | :webkit | :quiet_webkit | :webkit_debug | :rack_test
    default_wait_time: 20
    sleep_lapse: 5

  easypost_api_key: 'PSmNT4LTwGWMDaaDktCLgQ'

  include_analytics: true

  mercadolibre:
    app_key:        '***************'
    app_secret:     '********************************'
    callback_url:   'http://localhost:3000'
    site_country:   'MLA'

  mercadopago:
    old:
      client_id:     '9444'
      client_secret: '********************************'
    new:
      public_key: 'TEST-05343ed9-fc8c-4994-8723-f08059425e2d'
      access_token: 'TEST-12128-050413-ea866dd236eee1b6ca89e569b9eb88d6__LA_LB__-********'

  paperclip:
    s3_host_alias: 'gpcdn-test.s3.amazonaws.com'
    bucket: 'gpcdn-test'

  shopify:
    api_key: 'c01d66cff1008b8074b1e5a7d922fcfd'
    secret: '********************************'
