# frozen_string_literal: true

module Pioneer
  # Store controller
  class StoresController < Pioneer::ApplicationController
    before_filter :permissions_to_read?, except: %i[build_initial_data sanitize_params find_store store_attributes]
    before_filter :permissions_to_write?, except: %i[index build_initial_data sanitize_params find_store store_attributes]
    before_filter :build_initial_data
    before_filter :find_store, only: %i[edit update destroy]
    before_filter :sanitize_params, only: %i[create update]
    # before_filter :check_alternative_payment_methods, only: %i[create update]
    after_filter :create_gateway_credentials, only: %i[create update]
    layout 'bootstrap_layout'

    def index
      if current_user.role.is_a?('administrator')
        @stores = Mkp::Store.all.order(name: :asc)
                            .paginate(page: params[:page], per_page: 10)
      else
        @stores = Mkp::Store.where(id: current_user.role.store.id)
                            .paginate(page: params[:page], per_page: 10)
      end
    end

    def new
      @store = Mkp::Store.new
      build_installments
      @store.build_free_shipping
      @store.build_decidir_credential
      @store.build_cancelation_service_configuration
      @store.first_data_credentials.build
      @store.build_whitelist_configuration
      @store.build_aerolineas_argentinas_credential
      @store.build_renaper_configuration
      @credentials = @store.gateway_credentials
    end

    def create
      @store = Mkp::Store.new(store_attributes)

      if @store.save
        redirect_to stores_path
      else
        render :new
      end
    end

    def edit
      initialize_variables
    end

    def update
      @store.update_attributes(store_attributes)
      if @store.valid?
        redirect_to stores_path
      else
        flash[:error] = @store.errors.full_messages
        initialize_variables
        render :edit
      end
    end

    def destroy
      @store.destroy
      redirect_to stores_path
    end

    private

    def generate_catalog
      Gp::Application.load_tasks
      Rake::Task[ 'gp:catalog:index:generate' ].invoke
    end
    
    def build_initial_data
      @gateways = Mkp::Store::LABELS_GATEWAYS
      @shops = Mkp::Shop.all
    end
    
    def build_installments
      @store.installments.present? && return
    end
    
    def find_store
      @store = Mkp::Store.find(params[:id])
    end

    
    def store_attributes
      mkp_store_params = params.require(:mkp_store)
      mkp_store_params.except!('renaper_configuration_attributes') unless current_user.nacion_renaper_admin? or current_user.has_role?(:administrator)
      mkp_store_params.permit(:gateway, :logo, :title, :active, :email, :hostname,
        :inherit_free_shipping, :popup_image, :invoiceable,
        :percentage_fee, :image_dues, :image_dues_file_name,
        :telephone, :equivalent_points, :instagram, :facebook,
        :twitter, :abandoned_cart_email, :visa_puntos_equivalence,
        :visa_puntos_api_key, :visa_puntos_sube_equivalence,
        :visa_puntos_recargas_equivalence, :product_approval,
        :allow_orders_cancellation, :use_bines, :limit_amount,
        :insurance_source,
        :allow_meli_integration,
        :use_alternative_payment,
        preferred_sorting: [],
                    free_shipping_attributes: %i[id from to amount active],
                    decidir_credential_attributes: %i[id public_key
                      private_key avenida_distributed_site_id],
                      installments_attributes: %i[id number cft tea coef tna tna_coef cftna
                                                number _destroy],
                                                first_data_credentials_attributes: %i[id store_id
                                                          firstdata_store
                                                          user
                                                          password
                                                          certificate_password
                                                          ssl_cert ssl_cert_key
                                                          priority category],
                                                          whitelist_configuration_attributes: %i[active user_strategy
                                                           validation_strategy
                                                           unauthorized_user_message],
                    aerolineas_argentinas_credential_attributes: %i[id store_id
                                                                    user password
                                                                    partner_code
                                                                    partner_id
                                                                    partner_nbr
                                                                    points_equivalent],
                    cancelation_service_configuration_attributes: %i[generate_refund_shipments
                      generate_coupons
                                                                     generate_credit_notes
                                                                     cancel_payments],
                    renaper_configuration_attributes: %i[id active],
                    payment_credentials_attributes: %i[id name value _destroy],
                    gateway_alternative_strategies_attributes: %i[id gateway strategy]
                  )
                  end
                  
    def sanitize_params
      params[:mkp_store][:first_data_credentials_attributes].each do |fd_data|
        category = fd_data.last[:category].reject(&:blank?).join(',')
        fd_data.last[:category] = category
      end
      
      params[:mkp_store][:preferred_sorting] &&=
      params[:mkp_store][:preferred_sorting].split(':').map(&:to_sym)
      
      gateway_multiple = params[:mkp_store][:gateway_multiple]
      gateway = gateway_multiple.present? ? gateway_multiple.join(',') : ''
      params[:mkp_store][:gateway] = gateway
    end

    def create_gateway_credentials
      @store.gateway_credentials.destroy_all
      params[:mkp_store][:gateway_credential].each do |credential|
        next unless credential.last != ''

        GatewayCredential.create(store_id: @store.id,
                                 gateway_id: 1,
                                 name: credential.first,
                                 value: credential.last)
      end
    end

    def permissions_to_read?
      return unless cannot?(:read, 'Store')
      
      redirect_to home_url, alert: 'Have not permissions'
    end
    
    def permissions_to_write?
      return unless cannot?(:crud, 'Store')
      
      redirect_to stores_path, alert: 'Have not permissions'
    end

    def initialize_variables
      build_installments if @store.installments.blank?
      @store.build_decidir_credential if @store.decidir_credential.blank?
      if @store.cancelation_service_configuration.blank?
        @store.build_cancelation_service_configuration
      end
      @store.build_free_shipping if @store.free_shipping.blank?
      if @store.aerolineas_argentinas_credential.blank?
        @store.build_aerolineas_argentinas_credential
      end
      if @store.whitelist_configuration.blank?
        @store.build_whitelist_configuration
      end
      if @store.first_data_credentials.blank?
        @store.first_data_credentials.build
      end
      @credentials = @store.gateway_credentials
      if @store.renaper_configuration.blank?
        @store.build_renaper_configuration
      end
    end
  end
end
